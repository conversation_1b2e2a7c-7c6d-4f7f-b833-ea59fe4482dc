import os
import os.path as osp
import sys
import time
import numpy as np
from easydict import EasyDict as edict

C = edict()
config = C
cfg = C

C.seed = 12345

# 在Windows系统上使用cd命令代替pwd
if os.name == 'nt':  # Windows系统
    remoteip = os.popen('cd').read()
else:
    remoteip = os.popen('pwd').read()
C.root_dir = os.path.abspath(os.path.join(os.getcwd(), './'))
C.abs_dir = osp.realpath(".")

# Dataset config
"""Dataset Path"""
C.dataset_name = 'PIE-RGB-SAR'
C.dataset_path = osp.join(C.root_dir, 'datasets', 'PIE-RGB-SAR')
C.rgb_root_folder = osp.join(C.dataset_path, 'RGB')
C.rgb_format = '.tif'
C.gt_root_folder = osp.join(C.dataset_path, 'Label')
C.gt_format = '.tif'
C.gt_transform = True
# True when label 0 is invalid, you can also modify the function _transform_gt in dataloader.RGBXDataset
# True for most dataset valid, Faslse for MFNet(?)
C.x_root_folder = osp.join(C.dataset_path, 'SAR')
C.x_format = '.tif'
C.x_is_single_channel = True # 设置为True，因为SAR图像是单通道的
C.train_source = osp.join(C.dataset_path, "train.txt")
C.eval_source = osp.join(C.dataset_path, "val.txt")
C.is_test = False
C.num_train_imgs = 2432  # 数据集重新划分，训练集：验证集 = 1：1
C.num_eval_imgs = 2433    # 数据集重新划分，训练集：验证集 = 1：1
C.num_classes = 6        # 根据标签分析，有6个类别（0-5）
C.class_names = ['background', 'building', 'farmland', 'forest', 'water', 'road']  # 根据实际类别含义修改

"""Image Config"""
C.background = 255  # 设置为255，与RGBXDataset._gt_transform中的处理一致
C.image_height = 256  # 调整为更合适的尺寸
C.image_width = 256
C.norm_mean = np.array([0.348, 0.370, 0.309])  # 根据PIE-RGB-SAR数据集统计得到
C.norm_std = np.array([0.197, 0.174, 0.171])   # 根据PIE-RGB-SAR数据集统计得到

""" Settings for network, this would be different for each kind of model"""
C.backbone = 'swin_s' # Using Swin Transformer Small model
C.pretrained_model = osp.join(C.root_dir, 'swin_trans', 'swin_small_patch4_window7_224_22k.pth')
C.decoder = 'UPernet'  # 改为UPerNet decoder
C.decoder_embed_dim = 512  # UPerNet使用channels参数，但保留此配置以兼容
C.upernet_channels = 512  # UPerNet的通道数配置
C.optimizer = 'AdamW'

"""Train Config"""
C.lr = 1e-5  # 降低学习率，避免梯度爆炸
C.lr_power = 0.9
C.momentum = 0.9
C.weight_decay = 0.01
C.grad_clip = 1.0  # 添加梯度裁剪阈值
C.batch_size = 8  # 减小批处理大小以适应单张显卡
C.nepochs = 300
C.niters_per_epoch = C.num_train_imgs // C.batch_size  + 1
C.num_workers = 0  # Windows系统下设置为0，避免多进程问题
C.train_scale_array = [0.5, 0.75, 1, 1.25, 1.5, 1.75]
C.warm_up_epoch = 20  # 增加预热轮数，使训练更加稳定

# 日志配置
C.log_loss_components = True  # 是否记录损失组件详情
C.log_frequency = 'epoch'     # 'batch' 或 'epoch' - 控制详细日志的频率

C.fix_bias = True
C.bn_eps = 1e-3
C.bn_momentum = 0.1

"""Loss Function Config"""
# ============================================================================
# 🎯 当前配置: 固定权重策略 - 基于训练经验优化
# ============================================================================

# 根据你的训练反馈：交叉熵和focal权重相近，上下文损失权重很小
# 改为固定权重策略，避免权重不稳定的问题

# 🚀 主要损失函数配置 - 经典组合 (CE + Dice + Focal)
C.loss_type = 'combined'    # 使用固定权重组合损失函数

C.ce_weight = 1.0           # CrossEntropy权重 - 基础分类损失
C.dice_weight = 0.8         # Dice Loss权重 - 处理类别不平衡，提高IoU
C.focal_weight = 0.6        # Focal Loss权重 - 处理困难样本，权重适中

# 🔧 其他损失函数权重 (当前不使用，但保持兼容性)
C.lovasz_weight = 0.0       # Lovasz Loss - 暂时不使用
C.boundary_weight = 0.0     # 边界损失 - 暂时不使用
C.context_weight = 0.0      # 上下文损失 - 训练中权重过小，暂时不使用
C.tversky_weight = 0.0      # Tversky Loss - 备选方案
C.iou_weight = 0.0          # IoU Loss - 备选方案

# 融合损失函数权重字典
C.loss_weights = {
    'ce_weight': C.ce_weight,
    'dice_weight': C.dice_weight,
    'focal_weight': C.focal_weight,
    'lovasz_weight': C.lovasz_weight,
    'boundary_weight': C.boundary_weight,
    'context_weight': C.context_weight,
    'tversky_weight': C.tversky_weight,
    'iou_weight': C.iou_weight
}

# Focal Loss 参数配置
C.focal_gamma = 2.0         # 降低gamma值，减少对困难样本的过度关注
                            # 1.0-2.0: 轻微类别不平衡
                            # 2.0-3.0: 中等类别不平衡
                            # 3.0+: 严重类别不平衡

C.focal_alpha = 0.25        # 保持标准设置
                            # 0.25: 标准设置
                            # 0.3-0.4: 正样本较少时
                            # 0.1-0.2: 负样本较少时

# 上下文损失参数配置 - 新增
C.context_kernel_size = 7   # 上下文窗口大小，用于空间一致性
C.context_dilation = 2      # 膨胀率，扩大感受野
C.context_reduction = 'mean' # 上下文损失的reduction方式

# 其他损失函数参数
C.label_smoothing = 0.05    # 标签平滑参数 [推荐范围: 0.0-0.2]
                            # 0.0: 无标签平滑
                            # 0.05-0.1: 轻微平滑，推荐
                            # 0.1-0.2: 较强平滑，防止过拟合

# 是否使用类别权重
C.use_class_weights = False

# 类别权重设置方式: 'manual', 'inverse_freq', 'sqrt_inverse_freq', None
C.class_weight_method = 'manual'

# 手动设置的类别权重 [background, building, farmland, forest, water, road]
C.manual_class_weights = [0.5, 1.0, 1.2, 1.1, 1.3, 1.5]

# 根据设置方式确定最终的类别权重
if C.use_class_weights and C.class_weight_method == 'manual':
    C.class_weights = C.manual_class_weights
else:
    C.class_weights = None  # 运行时根据数据集统计计算

# 自适应损失函数参数（当loss_type='adaptive'时使用）

# 自适应损失函数的初始权重
C.adaptive_initial_weights = {
    'ce': 1.0,                  # CrossEntropy初始权重
    'focal': 1.0,               # Focal Loss初始权重
    'dice': 1.0,                # Dice Loss初始权重
    'lovasz': 0.5               # Lovasz Loss初始权重
}

# 自适应权重约束参数
C.adaptive_min_weight = 0.1
C.adaptive_max_weight = 2.0

# 自适应调整参数
C.adaptive_weight_decay = 0.01
C.adaptive_temperature = 1.0

# 权重学习率（相对于主学习率的倍数）
C.adaptive_weight_lr_ratio = 0.1

# 自适应策略配置
C.adaptive_strategy = 'gradient'  # 'gradient', 'performance', 'hybrid'
                                 # gradient: 基于梯度信息调整
                                 # performance: 基于性能指标调整
                                 # hybrid: 混合策略

# 权重更新频率
C.adaptive_update_frequency = 1   # 每N个epoch更新一次权重 [1表示每个batch都更新]

# 权重冻结策略
C.adaptive_freeze_after_epochs = None  # 在指定epoch后冻结权重，None表示不冻结

# 兼容性配置字典
C.adaptive_loss_config = {
    'initial_weights': C.adaptive_initial_weights,
    'min_weight': C.adaptive_min_weight,
    'max_weight': C.adaptive_max_weight,
    'weight_decay': C.adaptive_weight_decay,
    'temperature': C.adaptive_temperature,
    'focal_gamma': C.focal_gamma,
    'focal_alpha': C.focal_alpha,
    'label_smoothing': C.label_smoothing
}

# 预设配置快速切换

def apply_loss_preset(preset_name):
    """
    应用预设的损失函数配置
    preset_name: 'balanced', 'boundary', 'iou', 'simplified', 'imbalanced', 'adaptive'
    """
    global C

    if preset_name == 'balanced':
        # 平衡型配置 - 适用于一般情况
        C.loss_type = 'combined'
        C.ce_weight = 1.0
        C.focal_weight = 1.0
        C.dice_weight = 1.0
        C.lovasz_weight = 0.5
        C.focal_gamma = 2.0
        C.focal_alpha = 0.25

    elif preset_name == 'boundary':
        # 边界型配置 - 注重分割边界质量
        C.loss_type = 'combined'
        C.ce_weight = 0.8
        C.focal_weight = 1.2
        C.dice_weight = 1.5
        C.lovasz_weight = 0.8
        C.focal_gamma = 2.5
        C.focal_alpha = 0.3

    elif preset_name == 'iou':
        # IoU型配置 - 注重IoU指标
        C.loss_type = 'combined'
        C.ce_weight = 1.2
        C.focal_weight = 0.8
        C.dice_weight = 0.8
        C.lovasz_weight = 1.0
        C.focal_gamma = 1.5
        C.focal_alpha = 0.25

    elif preset_name == 'simplified':
        # 简化配置 - 交叉熵 + Focal + 上下文损失（已废弃，效果不佳）
        C.loss_type = 'simplified_combined'
        C.ce_weight = 1.0
        C.focal_weight = 0.8
        C.context_weight = 0.6
        C.dice_weight = 0.0
        C.lovasz_weight = 0.0
        C.focal_gamma = 2.0
        C.focal_alpha = 0.25

    elif preset_name == 'multimodal':
        # 多模态配置 - 交叉熵 + Dice + 边界损失（推荐用于RGB-SAR分割）
        C.loss_type = 'multimodal_combined'
        C.ce_weight = 1.0
        C.dice_weight = 1.0
        C.boundary_weight = 0.5
        C.focal_weight = 0.0
        C.context_weight = 0.0
        C.lovasz_weight = 0.0
        C.focal_gamma = 2.0
        C.focal_alpha = 0.25

    elif preset_name == 'imbalanced':
        # 不平衡配置 - 处理严重类别不平衡
        C.loss_type = 'combined'
        C.ce_weight = 1.0
        C.focal_weight = 2.0
        C.dice_weight = 1.0
        C.lovasz_weight = 0.5
        C.focal_gamma = 3.5
        C.focal_alpha = 0.4
        C.manual_class_weights = [0.3, 1.0, 1.5, 1.3, 1.8, 2.0]

    elif preset_name == 'adaptive':
        # 自适应配置 - 让模型自动学习最优权重
        C.loss_type = 'adaptive'
        C.adaptive_initial_weights = {
            'ce': 1.0,
            'focal': 1.0,
            'dice': 1.0,
            'lovasz': 0.5
        }
        C.adaptive_min_weight = 0.1
        C.adaptive_max_weight = 2.0
        C.adaptive_weight_decay = 0.01
        C.adaptive_temperature = 1.0
        C.adaptive_weight_lr_ratio = 0.1
        C.focal_gamma = 2.0
        C.focal_alpha = 0.25

    # 更新权重字典
    C.loss_weights = {
        'ce_weight': C.ce_weight,
        'dice_weight': C.dice_weight,
        'boundary_weight': getattr(C, 'boundary_weight', 0.0),
        'focal_weight': C.focal_weight,
        'context_weight': getattr(C, 'context_weight', 0.0),
        'lovasz_weight': C.lovasz_weight
    }

    # 更新自适应配置字典
    C.adaptive_loss_config = {
        'initial_weights': C.adaptive_initial_weights,
        'min_weight': C.adaptive_min_weight,
        'max_weight': C.adaptive_max_weight,
        'weight_decay': C.adaptive_weight_decay,
        'temperature': C.adaptive_temperature,
        'focal_gamma': C.focal_gamma,
        'focal_alpha': C.focal_alpha,
        'label_smoothing': C.label_smoothing
    }

    if C.use_class_weights and C.class_weight_method == 'manual':
        C.class_weights = C.manual_class_weights

# ============================================================================
# 预设配置应用 - 选择适合的配置
# ============================================================================

# ============================================================================
# 💡 当前使用的固定权重策略已在上面配置完成
# ============================================================================

# ============================================================================
# 🚨 简化损失函数策略 - 基于120epoch训练结果优化
# ============================================================================
# 分析：Dice Loss过高(0.579)，性能不如单CE，需要简化组合

# 策略1: 极简组合 - CE + Focal (移除Dice)
C.loss_type = 'combined'    # 强制设置为固定权重
C.ce_weight = 1.0           # 主要损失函数
C.dice_weight = 0.0         # 移除Dice Loss（过高，影响性能）
C.focal_weight = 0.5        # 降低Focal权重，辅助处理困难样本
C.lovasz_weight = 0.0

# 强制更新权重字典
C.loss_weights = {
    'ce_weight': C.ce_weight,
    'dice_weight': C.dice_weight,
    'focal_weight': C.focal_weight,
    'lovasz_weight': C.lovasz_weight,
    'boundary_weight': getattr(C, 'boundary_weight', 0.0),
    'context_weight': getattr(C, 'context_weight', 0.0),
    'tversky_weight': getattr(C, 'tversky_weight', 0.0),
    'iou_weight': getattr(C, 'iou_weight', 0.0)
}

print("🎯 简化损失函数策略: CE(1.0) + Focal(0.5)")
print("📊 基于120epoch训练结果优化 - 移除高损失的Dice")
print(f"🔧 当前损失函数类型: {C.loss_type}")
print(f"⚖️  权重配置: CE={C.ce_weight}, Dice={C.dice_weight}, Focal={C.focal_weight}")
print(f"📦 权重字典: {C.loss_weights}")
print("💡 如果性能仍不佳，可尝试纯CE损失函数")

# ============================================================================
# 🚨 重要提醒：确保配置正确
# ============================================================================
if C.loss_type == 'adaptive':
    print("⚠️  警告：当前仍在使用自适应权重！")
    print("💡 如需使用固定权重，请确保 C.loss_type = 'combined'")
elif C.loss_type == 'combined':
    print("✅ 确认：正在使用固定权重组合损失函数")
else:
    print(f"ℹ️  当前损失函数类型: {C.loss_type}")

# ============================================================================
# 其他损失函数组合选项 - 可以通过修改上面的权重来切换
# ============================================================================

def apply_fixed_weight_strategy(strategy='classic'):
    """
    应用固定权重策略

    Args:
        strategy: 策略名称
            - 'classic': 经典组合 CE + Dice + Focal
            - 'boundary': 边界优化 CE + Dice + Boundary
            - 'tversky': Tversky组合 CE + Tversky + Focal
            - 'compound': 复合损失 CE + Compound + Focal
            - 'simple': 简化组合 CE + Focal
            - 'iou_focused': IoU优化 CE + IoU + Dice
    """
    global C

    if strategy == 'classic':
        # 经典组合：CE + Dice + Focal
        C.loss_type = 'combined'
        C.ce_weight = 1.0
        C.dice_weight = 0.5
        C.focal_weight = 0.5
        C.lovasz_weight = 0.0
        C.boundary_weight = 0.0
        C.context_weight = 0.0
        print("🎯 经典组合: CE + Dice + Focal")

    elif strategy == 'boundary':
        # 边界优化：CE + Dice + Boundary
        C.loss_type = 'multimodal_combined'
        C.ce_weight = 1.0
        C.dice_weight = 0.8
        C.boundary_weight = 0.5
        C.focal_weight = 0.0
        C.lovasz_weight = 0.0
        C.context_weight = 0.0
        print("🎯 边界优化: CE + Dice + Boundary")

    elif strategy == 'tversky':
        # Tversky组合：CE + Tversky + Focal (需要实现TverskyLoss)
        C.loss_type = 'combined'
        C.ce_weight = 1.0
        C.dice_weight = 0.0  # 用Tversky替代Dice
        C.focal_weight = 0.6
        C.lovasz_weight = 0.0
        C.boundary_weight = 0.0
        C.context_weight = 0.0
        # 添加Tversky参数
        C.tversky_weight = 0.8
        C.tversky_alpha = 0.3  # 控制假阳性权重
        C.tversky_beta = 0.7   # 控制假阴性权重
        print("🎯 Tversky组合: CE + Tversky + Focal")

    elif strategy == 'simple':
        # 简化组合：CE + Focal
        C.loss_type = 'combined'
        C.ce_weight = 1.0
        C.focal_weight = 0.8
        C.dice_weight = 0.0
        C.lovasz_weight = 0.0
        C.boundary_weight = 0.0
        C.context_weight = 0.0
        print("🎯 简化组合: CE + Focal")

    elif strategy == 'iou_focused':
        # IoU优化：CE + IoU + Dice
        C.loss_type = 'combined'
        C.ce_weight = 1.0
        C.dice_weight = 0.8
        C.lovasz_weight = 0.6  # Lovasz作为IoU代理
        C.focal_weight = 0.0
        C.boundary_weight = 0.0
        C.context_weight = 0.0
        print("🎯 IoU优化: CE + Dice + Lovasz")

    # 更新权重字典
    C.loss_weights = {
        'ce_weight': C.ce_weight,
        'dice_weight': C.dice_weight,
        'boundary_weight': getattr(C, 'boundary_weight', 0.0),
        'focal_weight': C.focal_weight,
        'context_weight': getattr(C, 'context_weight', 0.0),
        'lovasz_weight': C.lovasz_weight,
        'tversky_weight': getattr(C, 'tversky_weight', 0.0),
        'iou_weight': getattr(C, 'iou_weight', 0.0)
    }

# 注意：默认策略已在上面直接配置，无需重复调用
# apply_fixed_weight_strategy('classic')  # 已注释，避免重复配置

# ============================================================================
# 快速切换到自适应损失函数
# ============================================================================
def switch_to_adaptive():
    """快速切换到自适应损失函数"""
    global C
    C.loss_type = 'adaptive'
    print("已切换到自适应损失函数")
    print("权重将在训练过程中自动调整")

def switch_to_combined():
    """快速切换到固定权重融合损失函数"""
    global C
    C.loss_type = 'combined'
    print("已切换到固定权重融合损失函数")

def switch_to_traditional():
    """快速切换到传统CrossEntropy损失函数"""
    global C
    C.loss_type = 'ce'
    print("已切换到传统CrossEntropy损失函数")

# ============================================================================
# 🚀 基于训练结果的快速切换函数
# ============================================================================

def switch_to_simple_combined():
    """切换到简化组合：CE + Focal"""
    global C
    C.loss_type = 'combined'
    C.ce_weight = 1.0
    C.dice_weight = 0.0
    C.focal_weight = 0.5
    C.lovasz_weight = 0.0
    C.loss_weights.update({
        'ce_weight': C.ce_weight,
        'dice_weight': C.dice_weight,
        'focal_weight': C.focal_weight,
        'lovasz_weight': C.lovasz_weight
    })
    print("✅ 已切换到简化组合: CE(1.0) + Focal(0.5)")

def switch_to_pure_ce():
    """切换到纯CrossEntropy损失函数"""
    global C
    C.loss_type = 'ce'
    print("✅ 已切换到纯CrossEntropy损失函数")
    print("💡 这是性能基准，如果多损失组合不如此配置，建议使用此设置")

def switch_to_ce_focal_minimal():
    """切换到最小化CE+Focal组合"""
    global C
    C.loss_type = 'combined'
    C.ce_weight = 1.0
    C.dice_weight = 0.0
    C.focal_weight = 0.3  # 更小的Focal权重
    C.lovasz_weight = 0.0
    C.loss_weights.update({
        'ce_weight': C.ce_weight,
        'dice_weight': C.dice_weight,
        'focal_weight': C.focal_weight,
        'lovasz_weight': C.lovasz_weight
    })
    print("✅ 已切换到最小化组合: CE(1.0) + Focal(0.3)")

# 使用示例：
# 在训练脚本中调用 config.switch_to_adaptive() 来切换到自适应模式

"""Eval Config"""
C.eval_iter = 25
C.eval_stride_rate = 2 / 3
C.eval_scale_array = [1]  # 使用单一尺度进行评估
C.eval_flip = False  # 不使用翻转增强
C.eval_crop_size = [256, 256]  # 调整为与训练图像尺寸一致
C.eval_batch_size = 12  # 验证时的batch size，比训练时更大
C.eval_frequency = 5  # 每5轮进行一次验证

"""Store Config"""
C.checkpoint_start_epoch = 1  # 从第一轮开始保存检查点
C.checkpoint_step = 5  # 每5轮保存一次检查点

"""Resume Config"""
C.resume_from = None  # 从指定检查点继续训练
C.resume_model = None  # 兼容旧版本代码

"""Path Config"""
def add_path(path):
    if path not in sys.path:
        sys.path.insert(0, path)
add_path(osp.join(C.root_dir))

C.log_dir = osp.abspath('log_' + C.dataset_name + '_' + C.backbone)
C.tb_dir = osp.abspath(osp.join(C.log_dir, "tb"))
C.log_dir_link = C.log_dir
C.checkpoint_dir = osp.abspath(osp.join(C.log_dir, "checkpoint"))

exp_time = time.strftime('%Y_%m_%d_%H_%M_%S', time.localtime())
C.log_file = C.log_dir + '/log_' + exp_time + '.log'
C.link_log_file = C.log_file + '/log_last.log'
C.val_log_file = C.log_dir + '/val_' + exp_time + '.log'
C.link_val_log_file = C.log_dir + '/val_last.log'

# ============================================================================
# 🔒 最终配置确认 - 确保配置不被覆盖
# ============================================================================
print("\n" + "="*60)
print("🔒 最终配置确认")
print("="*60)
print(f"损失函数类型: {C.loss_type}")
print(f"CE权重: {C.ce_weight}")
print(f"Dice权重: {C.dice_weight}")
print(f"Focal权重: {C.focal_weight}")
print(f"Lovasz权重: {C.lovasz_weight}")

if C.loss_type == 'combined':
    print("✅ 配置正确：使用固定权重组合损失函数")
else:
    print(f"❌ 配置错误：loss_type = {C.loss_type}")
    print("🔧 正在强制修正...")
    C.loss_type = 'combined'
    print("✅ 已强制修正为 'combined'")

print("="*60)

if __name__ == '__main__':
    print(f"训练轮数: {C.nepochs}")